import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  // 只处理 API 请求
  if (request.nextUrl.pathname.startsWith('/api/')) {
    // 获取客户端真实IP地址
    const forwarded = request.headers.get('x-forwarded-for');
    const realIp = request.headers.get('x-real-ip');
    const cfConnectingIp = request.headers.get('cf-connecting-ip');
    const remoteAddr = request.headers.get('remote-addr');

    // 尝试多种方式获取IP
    let clientIp = request.ip;

    if (!clientIp && forwarded) {
      clientIp = forwarded.split(',')[0].trim();
    }

    if (!clientIp && realIp) {
      clientIp = realIp;
    }

    if (!clientIp && cfConnectingIp) {
      clientIp = cfConnectingIp;
    }

    if (!clientIp && remoteAddr) {
      clientIp = remoteAddr;
    }

    // 如果还是没有获取到，尝试从其他来源
    if (!clientIp) {
      // 在开发环境下，我们可以使用一个模拟的IP
      if (process.env.NODE_ENV === 'development') {
        clientIp = '*************'; // 模拟的内网IP
      } else {
        clientIp = '127.0.0.1';
      }
    }

    // 添加详细的IP获取日志
    console.log('🌐 [Middleware] IP地址获取详情:', {
      url: request.nextUrl.pathname,
      method: request.method,
      原始IP来源: {
        'request.ip': request.ip,
        'x-forwarded-for': forwarded,
        'x-real-ip': realIp,
        'cf-connecting-ip': cfConnectingIp,
        'remote-addr': remoteAddr,
      },
      最终确定IP: clientIp,
      环境: process.env.NODE_ENV,
      时间戳: new Date().toISOString()
    });

    // 创建新的请求头
    const requestHeaders = new Headers(request.headers);

    // 设置真实IP相关的头部
    requestHeaders.set('x-forwarded-for', clientIp);
    requestHeaders.set('x-real-ip', clientIp);
    requestHeaders.set('x-client-ip', clientIp);

    // 创建新的响应，将修改后的头部传递给后端
    const response = NextResponse.rewrite(request.nextUrl, {
      request: {
        headers: requestHeaders,
      },
    });

    return response;
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * 匹配所有 API 路由
     */
    '/api/:path*',
  ],
};
