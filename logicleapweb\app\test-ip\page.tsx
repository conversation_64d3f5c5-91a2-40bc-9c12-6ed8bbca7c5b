'use client'

import { useState } from 'react';
import { <PERSON><PERSON>, Card, Typography, Space, Divider } from 'antd';
import request from '@/lib/request';

const { Title, Text, Paragraph } = Typography;

export default function TestIPPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  // 测试获取当前IP位置
  const testCurrentIP = async () => {
    setLoading(true);
    try {
      console.log('🧪 [Frontend] 开始测试当前IP获取...');
      
      const response = await request.get('/api/v1/ip-location/current');
      
      console.log('📥 [Frontend] 收到响应:', response);
      
      setResult({
        type: 'current-ip',
        data: response.data,
        timestamp: new Date().toISOString()
      });
    } catch (error: any) {
      console.error('❌ [Frontend] 请求失败:', error);
      setResult({
        type: 'error',
        error: error.message,
        response: error.response?.data,
        timestamp: new Date().toISOString()
      });
    } finally {
      setLoading(false);
    }
  };

  // 测试查询指定IP
  const testQueryIP = async () => {
    setLoading(true);
    try {
      console.log('🧪 [Frontend] 开始测试IP查询...');
      
      const testIP = '*******'; // Google DNS
      const response = await request.get(`/api/v1/ip-location/query?ip=${testIP}&includeRisk=false`);
      
      console.log('📥 [Frontend] 收到响应:', response);
      
      setResult({
        type: 'query-ip',
        testIP,
        data: response.data,
        timestamp: new Date().toISOString()
      });
    } catch (error: any) {
      console.error('❌ [Frontend] 请求失败:', error);
      setResult({
        type: 'error',
        error: error.message,
        response: error.response?.data,
        timestamp: new Date().toISOString()
      });
    } finally {
      setLoading(false);
    }
  };

  // 测试登录接口（观察IP日志）
  const testLoginIP = async () => {
    setLoading(true);
    try {
      console.log('🧪 [Frontend] 开始测试登录IP获取...');
      
      // 这里故意使用错误的登录信息，只是为了触发IP获取逻辑
      const response = await request.post('/api/user-auth/password', {
        phone: '12345678901',
        password: 'test123'
      });
      
      console.log('📥 [Frontend] 登录响应:', response);
      
      setResult({
        type: 'login-test',
        data: response.data,
        timestamp: new Date().toISOString()
      });
    } catch (error: any) {
      console.error('📝 [Frontend] 登录测试完成 (预期失败):', error);
      setResult({
        type: 'login-test',
        error: error.message,
        response: error.response?.data,
        note: '这是预期的失败，主要用于观察IP获取日志',
        timestamp: new Date().toISOString()
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>🧪 IP地址获取测试页面</Title>
      
      <Paragraph>
        这个页面用于测试前端到后端的IP地址传递和获取功能。
        请打开浏览器开发者工具的控制台，以及后端服务器的日志，观察IP获取过程。
      </Paragraph>

      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        
        {/* 测试按钮区域 */}
        <Card title="🎯 测试功能" size="small">
          <Space wrap>
            <Button 
              type="primary" 
              loading={loading}
              onClick={testCurrentIP}
            >
              测试获取当前IP位置
            </Button>
            
            <Button 
              loading={loading}
              onClick={testQueryIP}
            >
              测试查询指定IP (*******)
            </Button>
            
            <Button 
              loading={loading}
              onClick={testLoginIP}
              danger
            >
              测试登录IP获取 (会失败)
            </Button>
          </Space>
        </Card>

        {/* 结果显示区域 */}
        {result && (
          <Card title="📊 测试结果" size="small">
            <div style={{ background: '#f5f5f5', padding: '16px', borderRadius: '6px' }}>
              <pre style={{ margin: 0, fontSize: '12px', lineHeight: '1.4' }}>
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          </Card>
        )}

        <Divider />

        {/* 说明信息 */}
        <Card title="📋 观察要点" size="small">
          <Space direction="vertical">
            <div>
              <Text strong>🌐 前端中间件日志：</Text>
              <Text>查看浏览器控制台，观察 [Middleware] 标记的日志</Text>
            </div>
            
            <div>
              <Text strong>🖥️ 后端IP提取日志：</Text>
              <Text>查看后端控制台，观察 [Backend] 标记的日志</Text>
            </div>
            
            <div>
              <Text strong>🔐 登录日志：</Text>
              <Text>查看后端控制台，观察 [LoginLog] 标记的日志</Text>
            </div>
            
            <div>
              <Text strong>🔍 重点观察：</Text>
              <Text>IP地址是否从前端正确传递到后端，以及各个环节的IP获取情况</Text>
            </div>
          </Space>
        </Card>

        {/* 环境信息 */}
        <Card title="🌍 当前环境信息" size="small">
          <Space direction="vertical">
            <Text>浏览器 User-Agent: {typeof window !== 'undefined' ? navigator.userAgent : '服务端渲染'}</Text>
            <Text>当前时间: {new Date().toISOString()}</Text>
            <Text>页面URL: {typeof window !== 'undefined' ? window.location.href : '服务端渲染'}</Text>
          </Space>
        </Card>

      </Space>
    </div>
  );
}
