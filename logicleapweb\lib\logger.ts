/**
 * 前端日志工具
 * 统一管理前端日志输出，支持控制台和页面显示
 */

export interface LogEntry {
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  category: string;
  message: string;
  data?: any;
}

class FrontendLogger {
  private logs: LogEntry[] = [];
  private maxLogs = 100;
  private listeners: ((logs: LogEntry[]) => void)[] = [];

  /**
   * 添加日志监听器
   */
  addListener(callback: (logs: LogEntry[]) => void) {
    this.listeners.push(callback);
  }

  /**
   * 移除日志监听器
   */
  removeListener(callback: (logs: LogEntry[]) => void) {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners() {
    this.listeners.forEach(callback => callback([...this.logs]));
  }

  /**
   * 添加日志条目
   */
  private addLog(level: LogEntry['level'], category: string, message: string, data?: any) {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      category,
      message,
      data
    };

    // 添加到内存日志
    this.logs.push(entry);
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }

    // 输出到控制台
    const consoleMessage = `${entry.timestamp} [${category}] ${message}`;
    switch (level) {
      case 'error':
        console.error(consoleMessage, data || '');
        break;
      case 'warn':
        console.warn(consoleMessage, data || '');
        break;
      case 'debug':
        console.debug(consoleMessage, data || '');
        break;
      default:
        console.log(consoleMessage, data || '');
    }

    // 通知监听器
    this.notifyListeners();
  }

  /**
   * 信息日志
   */
  info(category: string, message: string, data?: any) {
    this.addLog('info', category, message, data);
  }

  /**
   * 警告日志
   */
  warn(category: string, message: string, data?: any) {
    this.addLog('warn', category, message, data);
  }

  /**
   * 错误日志
   */
  error(category: string, message: string, data?: any) {
    this.addLog('error', category, message, data);
  }

  /**
   * 调试日志
   */
  debug(category: string, message: string, data?: any) {
    this.addLog('debug', category, message, data);
  }

  /**
   * 获取所有日志
   */
  getLogs(): LogEntry[] {
    return [...this.logs];
  }

  /**
   * 清空日志
   */
  clear() {
    this.logs = [];
    console.clear();
    this.info('Logger', '日志已清空');
  }

  /**
   * 获取格式化的日志文本
   */
  getFormattedLogs(): string[] {
    return this.logs.map(log => {
      const time = new Date(log.timestamp).toLocaleTimeString();
      const level = log.level.toUpperCase().padEnd(5);
      return `[${time}] ${level} [${log.category}] ${log.message}`;
    });
  }
}

// 创建全局日志实例
export const logger = new FrontendLogger();

// 便捷的日志函数
export const logInfo = (category: string, message: string, data?: any) => 
  logger.info(category, message, data);

export const logWarn = (category: string, message: string, data?: any) => 
  logger.warn(category, message, data);

export const logError = (category: string, message: string, data?: any) => 
  logger.error(category, message, data);

export const logDebug = (category: string, message: string, data?: any) => 
  logger.debug(category, message, data);

// IP测试专用日志函数
export const ipTestLog = {
  init: (message: string, data?: any) => logger.info('IP-Test-Init', message, data),
  request: (message: string, data?: any) => logger.info('IP-Test-Request', message, data),
  response: (message: string, data?: any) => logger.info('IP-Test-Response', message, data),
  error: (message: string, data?: any) => logger.error('IP-Test-Error', message, data),
  middleware: (message: string, data?: any) => logger.info('IP-Test-Middleware', message, data),
  backend: (message: string, data?: any) => logger.info('IP-Test-Backend', message, data),
};

// 请求日志函数
export const requestLog = {
  start: (url: string, method: string, data?: any) => 
    logger.info('Request', `${method} ${url}`, data),
  success: (url: string, status: number, data?: any) => 
    logger.info('Response', `${status} ${url}`, data),
  error: (url: string, error: any) => 
    logger.error('Request-Error', `Failed ${url}`, error),
};

export default logger;
