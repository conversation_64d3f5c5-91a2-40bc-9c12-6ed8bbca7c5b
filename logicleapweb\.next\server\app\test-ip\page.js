/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/test-ip/page";
exports.ids = ["app/test-ip/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftest-ip%2Fpage&page=%2Ftest-ip%2Fpage&appPaths=%2Ftest-ip%2Fpage&pagePath=private-next-app-dir%2Ftest-ip%2Fpage.tsx&appDir=F%3A%5Clogicleap2%5Clogicleapweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Clogicleap2%5Clogicleapweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftest-ip%2Fpage&page=%2Ftest-ip%2Fpage&appPaths=%2Ftest-ip%2Fpage&pagePath=private-next-app-dir%2Ftest-ip%2Fpage.tsx&appDir=F%3A%5Clogicleap2%5Clogicleapweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Clogicleap2%5Clogicleapweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9100\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'test-ip',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/test-ip/page.tsx */ \"(rsc)/./app/test-ip/page.tsx\")), \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: \"/manifest.json\"\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: \"/manifest.json\"\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/test-ip/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/test-ip/page\",\n        pathname: \"/test-ip\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftest-ip%2Fpage&page=%2Ftest-ip%2Fpage&appPaths=%2Ftest-ip%2Fpage&pagePath=private-next-app-dir%2Ftest-ip%2Fpage.tsx&appDir=F%3A%5Clogicleap2%5Clogicleapweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Clogicleap2%5Clogicleapweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cantd%5C%5Cdist%5C%5Creset.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cantd%5C%5Cdist%5C%5Creset.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(ssr)/./app/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNsb2dpY2xlYXAyJTVDJTVDbG9naWNsZWFwd2ViJTVDJTVDYXBwJTVDJTVDcHJvdmlkZXJzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlByb3ZpZGVycyUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJGJTNBJTVDJTVDbG9naWNsZWFwMiU1QyU1Q2xvZ2ljbGVhcHdlYiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJGJTNBJTVDJTVDbG9naWNsZWFwMiU1QyU1Q2xvZ2ljbGVhcHdlYiU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNsb2dpY2xlYXAyJTVDJTVDbG9naWNsZWFwd2ViJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDYW50ZCU1QyU1Q2Rpc3QlNUMlNUNyZXNldC5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtKQUFvSCIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8/YWYyNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlByb3ZpZGVyc1wiXSAqLyBcIkY6XFxcXGxvZ2ljbGVhcDJcXFxcbG9naWNsZWFwd2ViXFxcXGFwcFxcXFxwcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cantd%5C%5Cdist%5C%5Creset.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Capp%5C%5Ctest-ip%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Capp%5C%5Ctest-ip%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/test-ip/page.tsx */ \"(ssr)/./app/test-ip/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNsb2dpY2xlYXAyJTVDJTVDbG9naWNsZWFwd2ViJTVDJTVDYXBwJTVDJTVDdGVzdC1pcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SkFBeUYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvP2VhM2QiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJGOlxcXFxsb2dpY2xlYXAyXFxcXGxvZ2ljbGVhcHdlYlxcXFxhcHBcXFxcdGVzdC1pcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Capp%5C%5Ctest-ip%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-redux */ \"(ssr)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./lib/store.ts\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_2__.Provider, {\n        store: _lib_store__WEBPACK_IMPORTED_MODULE_1__.store,\n        children: children\n    }, void 0, false, {\n        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\providers.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcHJvdmlkZXJzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFc0M7QUFDSDtBQUU1QixTQUFTRSxVQUFVLEVBQUVDLFFBQVEsRUFBaUM7SUFDbkUscUJBQ0UsOERBQUNILGlEQUFRQTtRQUFDQyxPQUFPQSw2Q0FBS0E7a0JBQ25CRTs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9hcHAvcHJvdmlkZXJzLnRzeD9jZTQ2Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xyXG5cclxuaW1wb3J0IHsgUHJvdmlkZXIgfSBmcm9tICdyZWFjdC1yZWR1eCdcclxuaW1wb3J0IHsgc3RvcmUgfSBmcm9tICdAL2xpYi9zdG9yZSdcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBQcm92aWRlcnMoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8UHJvdmlkZXIgc3RvcmU9e3N0b3JlfT5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9Qcm92aWRlcj5cclxuICApXHJcbn0gIl0sIm5hbWVzIjpbIlByb3ZpZGVyIiwic3RvcmUiLCJQcm92aWRlcnMiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./app/test-ip/page.tsx":
/*!******************************!*\
  !*** ./app/test-ip/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestIPPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _lib_request__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/request */ \"(ssr)/./lib/request.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst { Title, Text, Paragraph } = _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\nfunction TestIPPage() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [realPublicIP, setRealPublicIP] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 添加日志的辅助函数\n    const addLog = (message, data)=>{\n        const timestamp = new Date().toLocaleTimeString();\n        const logEntry = `[${timestamp}] ${message}`;\n        // 输出到控制台\n        if (data) {\n            console.log(message, data);\n        } else {\n            console.log(message);\n        }\n        // 添加到页面日志\n        setLogs((prev)=>[\n                ...prev.slice(-19),\n                logEntry\n            ]); // 保留最近20条日志\n    };\n    // 清空日志\n    const clearLogs = ()=>{\n        setLogs([]);\n        console.clear();\n        addLog(\"\\uD83E\\uDDF9 [Frontend] 日志已清空\");\n    };\n    // 页面加载时输出环境信息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        addLog(\"\\uD83C\\uDF10 [Frontend Init] 测试页面初始化...\");\n        console.log(\"\\uD83D\\uDDA5️ [Frontend Init] 浏览器环境信息:\", {\n            页面信息: {\n                URL: window.location.href,\n                域名: window.location.hostname,\n                端口: window.location.port,\n                协议: window.location.protocol,\n                路径: window.location.pathname\n            },\n            访问方式: {\n                是否本地访问: window.location.hostname === \"localhost\",\n                是否内网穿透: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\"),\n                访问类型: window.location.hostname === \"localhost\" ? \"本地开发\" : window.location.hostname.includes(\"ngrok\") ? \"ngrok穿透\" : window.location.hostname.includes(\"tunnel\") ? \"其他穿透\" : \"未知\"\n            },\n            浏览器信息: {\n                用户代理: navigator.userAgent,\n                语言: navigator.language,\n                平台: navigator.platform,\n                在线状态: navigator.onLine\n            },\n            网络信息: {\n                连接类型: navigator.connection?.effectiveType || \"未知\",\n                网络状态: navigator.connection?.downlink ? `${navigator.connection.downlink}Mbps` : \"未知\"\n            },\n            时间信息: {\n                本地时间: new Date().toISOString(),\n                时区: Intl.DateTimeFormat().resolvedOptions().timeZone,\n                时区偏移: new Date().getTimezoneOffset()\n            }\n        });\n        // 输出预期的测试流程\n        console.log(\"\\uD83D\\uDCCB [Frontend Init] 测试流程说明:\", {\n            测试目标: \"IP地址获取和传递功能验证\",\n            测试步骤: [\n                \"1. 获取真实公网IP (通过第三方API)\",\n                \"2. 测试当前IP位置获取 (后端API)\",\n                \"3. 测试指定IP查询 (*******)\",\n                \"4. 测试登录IP记录 (模拟登录失败)\"\n            ],\n            观察要点: [\n                \"前端请求日志 (\\uD83D\\uDCE4 [Frontend Request])\",\n                \"前端响应日志 (\\uD83D\\uDCE5 [Frontend Response])\",\n                \"中间件IP处理 (\\uD83C\\uDF10 [Middleware])\",\n                \"后端IP提取 (\\uD83D\\uDDA5️ [Backend])\",\n                \"登录IP记录 (\\uD83D\\uDD10 [LoginLog])\"\n            ],\n            预期结果: {\n                本地访问: \"IP为模拟值 (**************)\",\n                穿透访问: \"IP为真实公网IP\",\n                地理位置: \"根据IP解析出对应位置\"\n            }\n        });\n    }, []);\n    // 获取真实的公网IP地址\n    const getRealPublicIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83C\\uDF0D [Frontend] 开始获取真实公网IP...\");\n            // 使用多个IP查询服务，提高成功率\n            const ipServices = [\n                \"https://api.ipify.org?format=json\",\n                \"https://ipapi.co/json/\",\n                \"https://httpbin.org/ip\",\n                \"https://api.ip.sb/ip\",\n                \"https://ifconfig.me/ip\",\n                \"https://icanhazip.com\"\n            ];\n            for (const service of ipServices){\n                try {\n                    console.log(`🔍 尝试获取公网IP: ${service}`);\n                    const controller = new AbortController();\n                    const timeoutId = setTimeout(()=>controller.abort(), 5000);\n                    const response = await fetch(service, {\n                        method: \"GET\",\n                        signal: controller.signal\n                    });\n                    clearTimeout(timeoutId);\n                    if (!response.ok) continue;\n                    let data;\n                    const contentType = response.headers.get(\"content-type\");\n                    if (contentType && contentType.includes(\"application/json\")) {\n                        data = await response.json();\n                        const ip = data.ip || data.origin || data.query;\n                        if (ip && isValidIPAddress(ip)) {\n                            console.log(`✅ 成功获取公网IP: ${ip} (来源: ${service})`);\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    } else {\n                        const text = await response.text();\n                        const ip = text.trim();\n                        if (isValidIPAddress(ip)) {\n                            console.log(`✅ 成功获取公网IP: ${ip} (来源: ${service})`);\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    }\n                } catch (error) {\n                    console.log(`❌ 获取公网IP失败: ${service} - ${error}`);\n                    continue;\n                }\n            }\n            throw new Error(\"所有公网IP服务都无法访问\");\n        } catch (error) {\n            console.error(\"❌ [Frontend] 获取真实公网IP失败:\", error);\n            setResult({\n                type: \"error\",\n                error: error.message,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 验证IP地址格式\n    const isValidIPAddress = (ip)=>{\n        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n        return ipv4Regex.test(ip);\n    };\n    // 测试获取当前IP位置\n    const testCurrentIP = async ()=>{\n        setLoading(true);\n        try {\n            addLog(\"\\uD83E\\uDDEA [Frontend Test] 开始测试当前IP获取...\");\n            addLog(\"\\uD83C\\uDF10 [Frontend Test] 当前环境信息:\", {\n                页面URL: window.location.href,\n                域名: window.location.hostname,\n                是否内网穿透: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\"),\n                用户代理: navigator.userAgent.substring(0, 100) + \"...\",\n                时间戳: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 准备发送请求到: /api/v1/ip-location/current\");\n            const startTime = Date.now();\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"/api/v1/ip-location/current\");\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] 请求完成:\", {\n                耗时: `${endTime - startTime}ms`,\n                响应状态: response.status,\n                响应数据: response.data,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"current-ip\",\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            console.error(\"❌ [Frontend Test] 当前IP测试失败:\", {\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: error.response?.status,\n                响应数据: error.response?.data,\n                完整错误: error,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: error.response?.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试查询指定IP\n    const testQueryIP = async ()=>{\n        setLoading(true);\n        try {\n            const testIP = \"*******\"; // Google DNS\n            console.log(\"\\uD83E\\uDDEA [Frontend Test] 开始测试IP查询...\");\n            console.log(\"\\uD83C\\uDFAF [Frontend Test] 查询参数:\", {\n                目标IP: testIP,\n                IP类型: \"Google DNS服务器\",\n                预期位置: \"美国\",\n                包含风险评估: false,\n                时间戳: new Date().toISOString()\n            });\n            const queryUrl = `/api/v1/ip-location/query?ip=${testIP}&includeRisk=false`;\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 准备发送查询请求:\", queryUrl);\n            const startTime = Date.now();\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(queryUrl);\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] IP查询完成:\", {\n                查询IP: testIP,\n                耗时: `${endTime - startTime}ms`,\n                响应状态: response.status,\n                地理位置: response.data ? {\n                    国家: response.data.country,\n                    省份: response.data.province,\n                    城市: response.data.city,\n                    运营商: response.data.isp,\n                    置信度: response.data.confidence\n                } : \"无数据\",\n                完整响应: response.data,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"query-ip\",\n                testIP,\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            console.error(\"❌ [Frontend Test] IP查询失败:\", {\n                查询IP: \"*******\",\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: error.response?.status,\n                响应数据: error.response?.data,\n                可能原因: [\n                    \"后端服务未启动\",\n                    \"IP解析服务异常\",\n                    \"网络连接问题\"\n                ],\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: error.response?.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试登录接口（观察IP日志）\n    const testLoginIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend Test] 开始测试登录IP获取...\");\n            console.log(\"\\uD83D\\uDD10 [Frontend Test] 登录测试说明:\", {\n                目的: \"观察登录时的IP获取和记录过程\",\n                预期结果: \"登录失败（使用错误凭据）\",\n                观察重点: [\n                    \"IP地址获取\",\n                    \"登录日志记录\",\n                    \"错误处理\"\n                ],\n                测试凭据: {\n                    手机号: \"12345678910\",\n                    密码: \"123456 (错误密码)\"\n                },\n                时间戳: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 发送登录请求...\");\n            const startTime = Date.now();\n            // 这里故意使用错误的登录信息，只是为了触发IP获取逻辑\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"/api/user-auth/password\", {\n                phone: \"12345678901\",\n                password: \"123456\"\n            });\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] 登录响应 (意外成功):\", {\n                耗时: `${endTime - startTime}ms`,\n                响应状态: response.status,\n                响应数据: response.data,\n                注意: \"这不应该成功，请检查后端验证逻辑\",\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"login-test\",\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            console.log(\"\\uD83D\\uDCDD [Frontend Test] 登录测试完成 (预期失败):\", {\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: error.response?.status,\n                响应数据: error.response?.data,\n                分析: {\n                    是否预期失败: true,\n                    失败原因: \"使用了错误的登录凭据\",\n                    IP获取状态: \"应该已触发IP获取和日志记录\",\n                    后续检查: \"查看后端控制台的 [LoginLog] 日志\"\n                },\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"login-test\",\n                error: error.message,\n                response: error.response?.data,\n                note: \"这是预期的失败，主要用于观察IP获取日志\",\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"24px\",\n            maxWidth: \"1200px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                level: 2,\n                children: \"\\uD83E\\uDDEA IP地址获取测试页面\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                children: \"这个页面用于测试前端到后端的IP地址传递和获取功能。 请打开浏览器开发者工具的控制台，以及后端服务器的日志，观察IP获取过程。\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                direction: \"vertical\",\n                size: \"large\",\n                style: {\n                    width: \"100%\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDFAF 测试功能\",\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                wrap: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        type: \"primary\",\n                                        loading: loading,\n                                        onClick: getRealPublicIP,\n                                        style: {\n                                            background: \"#52c41a\",\n                                            borderColor: \"#52c41a\"\n                                        },\n                                        children: \"\\uD83C\\uDF0D 获取真实公网IP\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testCurrentIP,\n                                        children: \"测试获取当前IP位置\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testQueryIP,\n                                        children: \"测试查询指定IP (*******)\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testLoginIP,\n                                        danger: true,\n                                        children: \"测试登录IP获取 (会失败)\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        onClick: clearLogs,\n                                        style: {\n                                            marginLeft: \"12px\"\n                                        },\n                                        children: \"\\uD83E\\uDDF9 清空日志\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, this),\n                            realPublicIP && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: \"12px\",\n                                    padding: \"8px\",\n                                    background: \"#f6ffed\",\n                                    border: \"1px solid #b7eb8f\",\n                                    borderRadius: \"6px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    style: {\n                                        color: \"#52c41a\"\n                                    },\n                                    children: [\n                                        \"\\uD83C\\uDF0D 你的真实公网IP: \",\n                                        realPublicIP\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 9\n                    }, this),\n                    result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCA 测试结果\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: \"#f5f5f5\",\n                                padding: \"16px\",\n                                borderRadius: \"6px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                style: {\n                                    margin: 0,\n                                    fontSize: \"12px\",\n                                    lineHeight: \"1.4\"\n                                },\n                                children: JSON.stringify(result, null, 2)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCDD 实时前端日志\",\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"#000\",\n                                    color: \"#00ff00\",\n                                    padding: \"12px\",\n                                    borderRadius: \"6px\",\n                                    fontFamily: \"Monaco, Consolas, monospace\",\n                                    fontSize: \"12px\",\n                                    maxHeight: \"300px\",\n                                    overflowY: \"auto\"\n                                },\n                                children: logs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        color: \"#666\"\n                                    },\n                                    children: \"等待日志输出...\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 15\n                                }, this) : logs.map((log, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: \"2px\"\n                                        },\n                                        children: log\n                                    }, index, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: \"8px\",\n                                    fontSize: \"12px\",\n                                    color: \"#666\"\n                                },\n                                children: \"\\uD83D\\uDCA1 提示：这里显示前端日志，完整日志请查看浏览器控制台\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCB 观察要点\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83C\\uDF10 前端中间件日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看浏览器控制台，观察 [Middleware] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDDA5️ 后端IP提取日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [Backend] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD10 登录日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [LoginLog] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD0D 重点观察：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"IP地址是否从前端正确传递到后端，以及各个环节的IP获取情况\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 469,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83E\\uDD14 为什么本地开发获取到127.0.0.1？\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#fa8c16\"\n                                            },\n                                            children: \"\\uD83C\\uDFE0 本地开发环境：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"浏览器 → localhost:3000 → 后端API，所有请求都来自本机，所以IP是127.0.0.1\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#52c41a\"\n                                            },\n                                            children: \"\\uD83C\\uDF0D 生产环境：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"用户浏览器 → CDN/负载均衡 → Web服务器 → 后端API，能获取到真实公网IP\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#1890ff\"\n                                            },\n                                            children: \"\\uD83C\\uDFAD 模拟解决方案：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"中间件已配置在开发环境使用模拟公网IP (**************) 进行测试\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#722ed1\"\n                                            },\n                                            children: \"\\uD83E\\uDDEA 真实IP对比：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: '点击\"获取真实公网IP\"按钮，对比你的真实公网IP和后端获取的IP'\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 494,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDF0D 当前环境信息\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"浏览器 User-Agent: \",\n                                         false ? 0 : \"服务端渲染\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"当前时间: \",\n                                        new Date().toISOString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"页面URL: \",\n                                         false ? 0 : \"服务端渲染\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, this),\n                                 false && /*#__PURE__*/ 0\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 9\n                    }, this),\n                     false && /*#__PURE__*/ 0\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 377,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n        lineNumber: 369,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/test-ip/page.tsx\n");

/***/ }),

/***/ "(ssr)/./config/config.ts":
/*!**************************!*\
  !*** ./config/config.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_HOST: () => (/* binding */ API_HOST),\n/* harmony export */   API_PORT: () => (/* binding */ API_PORT),\n/* harmony export */   API_URL: () => (/* binding */ API_URL),\n/* harmony export */   BASE_URL: () => (/* binding */ BASE_URL),\n/* harmony export */   CONFIG: () => (/* binding */ CONFIG),\n/* harmony export */   DEV_CONFIG: () => (/* binding */ DEV_CONFIG),\n/* harmony export */   PROD_CONFIG: () => (/* binding */ PROD_CONFIG),\n/* harmony export */   SERVER_URL: () => (/* binding */ SERVER_URL),\n/* harmony export */   WS_URL: () => (/* binding */ WS_URL)\n/* harmony export */ });\n/*\r\n * @Author: Zwww <EMAIL>\r\n * @Date: 2025-04-27 23:58:17\r\n * @LastEditors: Zwww <EMAIL>\r\n * @LastEditTime: 2025-04-28 09:36:46\r\n * @FilePath: \\logicleapweb\\config\\config.ts\r\n * @Description: \r\n * \r\n * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. \r\n */ // 开发环境配置\nconst DEV_CONFIG = {\n    API_HOST: \"http://127.0.0.1\",\n    API_PORT: \"8601\",\n    BASE_URL: \"${API_HOST}:${API_PORT}\",\n    SERVER_URL: \"http://127.0.0.1:8602\",\n    API_URL: \"http://127.0.0.1:8003\",\n    WS_URL: \"http://localhost:8003\"\n};\n// 生产环境配置\nconst PROD_CONFIG = {\n    API_HOST: \"https://www.logicleapai.cn\",\n    API_PORT: \"8601\",\n    BASE_URL: \"https://www.logicleapai.cn\",\n    SERVER_URL: \"https://www.logicleapai.cn/server\",\n    API_URL: \"https://www.logicleapai.cn/api\",\n    WS_URL: \"wss://www.logicleapai.cn/api\",\n    WS_SERVER_URL: \"wss://logicleapai.cn/server/ws\"\n};\n// 根据环境导出相应配置\nconst CONFIG =  false ? 0 : DEV_CONFIG;\n// 导出单独的配置项,方便直接使用\nconst { API_HOST, API_PORT, BASE_URL, SERVER_URL, API_URL, WS_URL } = CONFIG;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./config/config.ts\n");

/***/ }),

/***/ "(ssr)/./lib/request.ts":
/*!************************!*\
  !*** ./lib/request.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   hasShownModal: () => (/* binding */ hasShownModal),\n/* harmony export */   resetModalFlag: () => (/* binding */ resetModalFlag)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _config_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../config/config */ \"(ssr)/./config/config.ts\");\n/* harmony import */ var _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Modal!=!antd */ \"(ssr)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/store */ \"(ssr)/./lib/store.ts\");\n\n\n\n\n\nconst request = axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n    baseURL: _config_config__WEBPACK_IMPORTED_MODULE_0__.API_URL,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\nlet isTokenExpiredGlobal = false;\n// 添加全局标志，用于控制弹框显示\nlet hasShownModal = false;\n// 添加重置方法\nconst resetModalFlag = ()=>{\n    hasShownModal = false;\n};\n// 刷新token相关变量\nlet isRefreshing = false;\nlet failedQueue = [];\n// 处理队列中的请求\nconst processQueue = (error, token = null)=>{\n    failedQueue.forEach(({ resolve, reject, config })=>{\n        if (error) {\n            reject(error);\n        } else {\n            if (token) {\n                config.headers.Authorization = token;\n            }\n            resolve(request(config));\n        }\n    });\n    failedQueue = [];\n};\n// 刷新token的函数\nconst refreshToken = async ()=>{\n    const refreshToken = localStorage.getItem(\"refreshToken\");\n    console.log(\"\\uD83D\\uDD04 开始刷新token，refreshToken:\", refreshToken ? `${refreshToken.substring(0, 20)}...` : \"无\");\n    if (!refreshToken) {\n        console.error(\"❌ 没有refreshToken，无法刷新\");\n        throw new Error(\"No refresh token available\");\n    }\n    try {\n        console.log(\"\\uD83D\\uDCE4 发送刷新token请求到:\", \"/api/router-guard/refresh-token\");\n        const response = await axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"/api/router-guard/refresh-token\", {\n            refreshToken: refreshToken\n        }, {\n            baseURL: _config_config__WEBPACK_IMPORTED_MODULE_0__.API_URL\n        });\n        console.log(\"\\uD83D\\uDCE5 刷新token响应:\", response.data);\n        if (response.data.code === 200) {\n            const { token, refreshToken: newRefreshToken } = response.data.data;\n            console.log(\"✅ 刷新token成功，新token:\", token ? `${token.substring(0, 20)}...` : \"无\");\n            localStorage.setItem(\"token\", token);\n            localStorage.setItem(\"refreshToken\", newRefreshToken);\n            return token;\n        } else {\n            console.error(\"❌ 刷新token失败，响应码:\", response.data.code, \"消息:\", response.data.message || response.data.msg);\n            throw new Error(`Token refresh failed: ${response.data.message || response.data.msg || \"Unknown error\"}`);\n        }\n    } catch (error) {\n        console.error(\"❌ 刷新token异常:\", error);\n        console.error(\"错误详情:\", {\n            message: error.message,\n            response: error.response?.data,\n            status: error.response?.status\n        });\n        // 刷新失败，清除所有token\n        localStorage.removeItem(\"token\");\n        localStorage.removeItem(\"refreshToken\");\n        localStorage.removeItem(\"user\");\n        (0,_lib_store__WEBPACK_IMPORTED_MODULE_1__.clearUser)();\n        throw error;\n    }\n};\n// 请求拦截器\nrequest.interceptors.request.use(async (config)=>{\n    // 添加请求日志\n    console.log(\"\\uD83D\\uDCE4 [Frontend Request] 发送请求:\", {\n        url: config.url,\n        method: config.method?.toUpperCase(),\n        baseURL: config.baseURL,\n        完整URL: `${config.baseURL}${config.url}`,\n        请求头: {\n            \"Content-Type\": config.headers[\"Content-Type\"],\n            \"Authorization\": config.headers[\"Authorization\"] ? \"已设置\" : \"未设置\",\n            \"User-Agent\": navigator.userAgent.substring(0, 50) + \"...\"\n        },\n        请求数据: config.data ? JSON.stringify(config.data).substring(0, 200) + \"...\" : \"无\",\n        时间戳: new Date().toISOString()\n    });\n    const token = localStorage.getItem(\"token\");\n    const refreshTokenValue = localStorage.getItem(\"refreshToken\");\n    // 检查请求的URL是否为登录接口或刷新token接口\n    if (config.url && (config.url.includes(\"/api/user-auth/password\") || config.url.includes(\"/api/router-guard/refresh-token\"))) {\n        return config; // 不拦截登录和刷新token请求\n    }\n    if (token) {\n        config.headers.Authorization = token;\n    } else if (refreshTokenValue && !isRefreshing) {\n        // 没有token但有refreshToken，尝试主动刷新\n        console.log(\"\\uD83D\\uDD04 请求拦截器检测到缺少token但有refreshToken，主动尝试刷新\");\n        try {\n            // 标记正在刷新，避免重复刷新\n            isRefreshing = true;\n            const newToken = await refreshToken();\n            console.log(\"✅ 请求拦截器中刷新token成功\");\n            // 设置新token到当前请求\n            config.headers.Authorization = newToken;\n            // 处理队列中的其他请求\n            processQueue(null, newToken);\n        } catch (refreshError) {\n            console.error(\"❌ 请求拦截器中刷新token失败:\", refreshError);\n            // 处理队列中的其他请求\n            processQueue(refreshError, null);\n            // 刷新失败，清除refreshToken并拒绝请求\n            handleLogout(\"请求拦截器中refreshToken刷新失败\");\n            return Promise.reject(new Error(\"Token刷新失败，请重新登录\"));\n        } finally{\n            isRefreshing = false;\n        }\n    } else if (!refreshTokenValue) {\n        console.warn(\"请求拦截器 - 未找到token和refreshToken\");\n    } else {\n        console.warn(\"请求拦截器 - 未找到token，但正在刷新中\");\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\nrequest.interceptors.response.use((response)=>{\n    // 添加响应日志\n    console.log(\"\\uD83D\\uDCE5 [Frontend Response] 收到响应:\", {\n        url: response.config.url,\n        method: response.config.method?.toUpperCase(),\n        状态码: response.status,\n        状态文本: response.statusText,\n        响应头: {\n            \"content-type\": response.headers[\"content-type\"],\n            \"content-length\": response.headers[\"content-length\"],\n            \"server\": response.headers[\"server\"]\n        },\n        响应数据预览: response.data ? JSON.stringify(response.data).substring(0, 300) + \"...\" : \"无\",\n        响应时间: new Date().toISOString()\n    });\n    if (response.config.url?.includes(\"/login/password\") && response.data?.code === 200) {\n        resetModalFlag();\n        isTokenExpiredGlobal = false; // 重置token过期标志\n    }\n    return response;\n}, async (error)=>{\n    const originalRequest = error.config;\n    const errorData = error.response?.data;\n    // 添加详细的错误日志\n    console.log(\"❌ [Frontend Error] 请求失败:\", {\n        请求信息: {\n            url: originalRequest?.url,\n            method: originalRequest?.method?.toUpperCase(),\n            baseURL: originalRequest?.baseURL,\n            完整URL: originalRequest ? `${originalRequest.baseURL}${originalRequest.url}` : \"未知\"\n        },\n        错误信息: {\n            状态码: error.response?.status,\n            状态文本: error.response?.statusText,\n            错误类型: error.name,\n            错误消息: error.message\n        },\n        响应数据: errorData,\n        网络信息: {\n            是否网络错误: !error.response,\n            是否超时: error.code === \"ECONNABORTED\",\n            是否取消: error.code === \"ERR_CANCELED\"\n        },\n        时间戳: new Date().toISOString()\n    });\n    // 处理401状态码的错误\n    if (error.response?.status === 401) {\n        console.log(\"\\uD83D\\uDEA8 [Frontend Auth] 收到401认证错误:\", {\n            url: originalRequest?.url,\n            status: error.response?.status,\n            errorData: errorData,\n            hasType: !!errorData?.type,\n            hasMsg: !!errorData?.msg,\n            详细分析: {\n                可能原因: [\n                    \"token过期\",\n                    \"无效token\",\n                    \"其他设备登录\",\n                    \"权限不足\"\n                ],\n                下一步处理: \"尝试token刷新或重新登录\"\n            }\n        });\n        // 处理token过期和其他设备登录的情况\n        // 检查多种可能的错误格式和消息\n        // 支持嵌套的错误结构（如 details 字段）\n        const detailsData = errorData?.details || errorData;\n        // 检查是否是其他设备登录的错误\n        const isOtherDeviceLogin = errorData?.type === \"OTHER_DEVICE_LOGIN\" || detailsData?.type === \"OTHER_DEVICE_LOGIN\" || errorData?.msg && errorData.msg.includes(\"账号已在其他设备登录\") || detailsData?.msg && detailsData.msg.includes(\"账号已在其他设备登录\") || errorData?.message && errorData.message.includes(\"账号已在其他设备登录\") || detailsData?.message && detailsData.message.includes(\"账号已在其他设备登录\");\n        console.log(\"\\uD83D\\uDD0D 检查其他设备登录状态:\", {\n            isOtherDeviceLogin,\n            errorData: errorData,\n            detailsData: detailsData,\n            hasShownModal\n        });\n        // 如果是其他设备登录，直接显示提示\n        if (isOtherDeviceLogin && !hasShownModal) {\n            hasShownModal = true;\n            _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].confirm({\n                title: \"账号异常\",\n                content: \"您的账号已在其他设备登录，当前登录已失效，请重新登录\",\n                okText: \"重新登录\",\n                maskClosable: false,\n                keyboard: true,\n                centered: true,\n                className: \"other-device-login-modal\",\n                closable: false,\n                cancelButtonProps: {\n                    style: {\n                        display: \"none\"\n                    }\n                },\n                onOk: ()=>{\n                    handleLogout(\"其他设备登录，当前会话失效\");\n                }\n            });\n            return Promise.reject(error);\n        }\n        // 处理token过期的情况，尝试无感刷新\n        // 使用上面已定义的 detailsData 变量\n        const isTokenExpired = [\n            \"TOKEN_EXPIRED\",\n            \"INVALID_TOKEN\"\n        ].includes(errorData?.type || detailsData?.type) || errorData?.msg && (errorData.msg.includes(\"登录已过期\") || errorData.msg.includes(\"token无效\") || errorData.msg.includes(\"token已过期\") || errorData.msg.includes(\"请先登录\")) || detailsData?.msg && (detailsData.msg.includes(\"登录已过期\") || detailsData.msg.includes(\"token无效\") || detailsData.msg.includes(\"token已过期\") || detailsData.msg.includes(\"请先登录\")) || errorData?.message && (errorData.message.includes(\"登录已过期\") || errorData.message.includes(\"token无效\") || errorData.message.includes(\"token已过期\") || errorData.message.includes(\"请先登录\")) || detailsData?.message && (detailsData.message.includes(\"登录已过期\") || detailsData.message.includes(\"token无效\") || detailsData.message.includes(\"token已过期\") || detailsData.message.includes(\"请先登录\")) || error.response?.status === 401 && (errorData?.code === 401 || detailsData?.code === 401);\n        console.log(\"\\uD83D\\uDD0D 检查token过期状态:\", {\n            isTokenExpired,\n            errorData: errorData,\n            detailsData: detailsData,\n            errorType: errorData?.type,\n            errorMsg: errorData?.msg,\n            errorMessage: errorData?.message,\n            errorCode: errorData?.code,\n            detailsType: detailsData?.type,\n            detailsMsg: detailsData?.msg,\n            detailsMessage: detailsData?.message,\n            detailsCode: detailsData?.code,\n            status: error.response?.status,\n            url: originalRequest?.url\n        });\n        if (isTokenExpired) {\n            console.log(\"\\uD83D\\uDD0D 检测到token过期，准备无感刷新\");\n            // 如果已经在刷新中，将请求加入队列\n            if (isRefreshing) {\n                console.log(\"⏳ 已在刷新中，将请求加入队列\");\n                return new Promise((resolve, reject)=>{\n                    failedQueue.push({\n                        resolve,\n                        reject,\n                        config: originalRequest\n                    });\n                });\n            }\n            // 如果没有refreshToken，直接登出\n            const refreshTokenValue = localStorage.getItem(\"refreshToken\");\n            if (!refreshTokenValue) {\n                console.log(\"❌ 没有refreshToken，直接登出\");\n                handleLogout(\"缺少refreshToken\");\n                return Promise.reject(error);\n            }\n            // 开始刷新token\n            console.log(\"\\uD83D\\uDD04 开始刷新token流程\");\n            isRefreshing = true;\n            try {\n                const newToken = await refreshToken();\n                console.log(\"✅ 刷新token成功，处理队列中的请求\");\n                processQueue(null, newToken);\n                // 重新发起原始请求\n                originalRequest.headers.Authorization = newToken;\n                console.log(\"\\uD83D\\uDD01 重新发起原始请求:\", originalRequest.url);\n                return request(originalRequest);\n            } catch (refreshError) {\n                console.error(\"❌ 刷新token失败:\", refreshError);\n                processQueue(refreshError, null);\n                handleLogout(\"refreshToken刷新失败\");\n                return Promise.reject(refreshError);\n            } finally{\n                isRefreshing = false;\n            }\n        }\n        // 其他401错误，直接登出\n        if (!isTokenExpiredGlobal) {\n            isTokenExpiredGlobal = true;\n            handleLogout(\"非token过期的401错误\");\n        }\n    }\n    // 错误仍然需要传递给调用者处理\n    return Promise.reject(error);\n});\n// 统一的登出处理函数\nconst handleLogout = (reason = \"未知原因\")=>{\n    console.log(\"\\uD83D\\uDEAA 执行登出操作，原因:\", reason);\n    console.log(\"\\uD83D\\uDDD1️ 清除localStorage中的认证信息\");\n    // 记录清除前的状态\n    const beforeClear = {\n        token: localStorage.getItem(\"token\") ? \"存在\" : \"不存在\",\n        refreshToken: localStorage.getItem(\"refreshToken\") ? \"存在\" : \"不存在\",\n        user: localStorage.getItem(\"user\") ? \"存在\" : \"不存在\"\n    };\n    console.log(\"清除前状态:\", beforeClear);\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    localStorage.removeItem(\"refreshToken\");\n    _lib_store__WEBPACK_IMPORTED_MODULE_1__.store.dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_1__.clearUser)());\n    console.log(\"✅ 登出处理完成\");\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (request);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/request.ts\n");

/***/ }),

/***/ "(ssr)/./lib/store.ts":
/*!**********************!*\
  !*** ./lib/store.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearUser: () => (/* binding */ clearUser),\n/* harmony export */   selectUserState: () => (/* binding */ selectUserState),\n/* harmony export */   setUser: () => (/* binding */ setUser),\n/* harmony export */   store: () => (/* binding */ store),\n/* harmony export */   userSlice: () => (/* binding */ userSlice)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/*\r\n * @Author: Zwww <EMAIL>\r\n * @Date: 2025-04-25 12:31:25\r\n * @LastEditors: Zwww <EMAIL>\r\n * @LastEditTime: 2025-05-01 13:19:36\r\n * @FilePath: \\logicleapweb\\lib\\store.ts\r\n * @Description: \r\n * \r\n * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. \r\n */ \nconst defaultUserState = {\n    userId: null,\n    nickName: \"\",\n    avatarUrl: \"\",\n    introduction: \"\",\n    gender: 0,\n    phone: \"\",\n    isLoggedIn: false,\n    roleId: null,\n    registerType: \"\"\n};\n// 从 localStorage 获取初始状态\nconst loadInitialState = ()=>{\n    if (false) {}\n    return {\n        userState: {\n            ...defaultUserState\n        }\n    };\n};\n// 创建 userSlice\nconst userSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: \"user\",\n    initialState: loadInitialState(),\n    reducers: {\n        setUser: (state, action)=>{\n            state.userState = {\n                ...state.userState,\n                ...action.payload,\n                isLoggedIn: true,\n                lastUpdateTime: Date.now()\n            };\n            if (false) {}\n        },\n        clearUser: (state)=>{\n            state.userState = {\n                userId: null,\n                nickName: \"\",\n                avatarUrl: \"\",\n                introduction: \"\",\n                gender: 0,\n                phone: \"\",\n                isLoggedIn: false,\n                roleId: null,\n                registerType: \"\",\n                lastUpdateTime: null\n            };\n            // 如果是在浏览器环境中，则清除localStorage中的用户信息\n            if (false) {}\n        }\n    }\n});\nconst { setUser, clearUser } = userSlice.actions;\n// 创建 store\nconst store = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.configureStore)({\n    reducer: {\n        [userSlice.name]: userSlice.reducer\n    },\n    middleware: (getDefaultMiddleware)=>getDefaultMiddleware({\n            serializableCheck: false\n        })\n});\nconst selectUserState = (state)=>state.user.userState;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/store.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"928949eb63fa\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9hcHAvZ2xvYmFscy5jc3M/MTgwMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjkyODk0OWViNjNmYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var antd_dist_reset_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! antd/dist/reset.css */ \"(rsc)/./node_modules/antd/dist/reset.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./providers */ \"(rsc)/./app/providers.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Logic Leap\",\n    description: \"为青少年打造的AI学习平台\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {}, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_3__.Providers, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQU1NQTtBQUpnQjtBQUNNO0FBQ1c7QUFJaEMsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFDYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSzs7MEJBQ1QsOERBQUNDOzs7OzswQkFFRCw4REFBQ0M7MEJBQ0MsNEVBQUNULGlEQUFTQTs4QkFDUEs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xyXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXHJcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcclxuaW1wb3J0ICdhbnRkL2Rpc3QvcmVzZXQuY3NzJ1xyXG5pbXBvcnQgeyBQcm92aWRlcnMgfSBmcm9tICcuL3Byb3ZpZGVycydcclxuXHJcbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcclxuXHJcbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XHJcbiAgdGl0bGU6ICdMb2dpYyBMZWFwJyxcclxuICBkZXNjcmlwdGlvbjogJ+S4uumdkuWwkeW5tOaJk+mAoOeahEFJ5a2m5Lmg5bmz5Y+wJyxcclxufVxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcclxuICBjaGlsZHJlbixcclxufToge1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcclxufSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cclxuICAgICAgPGhlYWQ+XHJcbiAgICAgIDwvaGVhZD5cclxuICAgICAgPGJvZHk+XHJcbiAgICAgICAgPFByb3ZpZGVycz5cclxuICAgICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICA8L1Byb3ZpZGVycz5cclxuICAgICAgPC9ib2R5PlxyXG4gICAgPC9odG1sPlxyXG4gIClcclxufVxyXG5cclxuIl0sIm5hbWVzIjpbImludGVyIiwiUHJvdmlkZXJzIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJoZWFkIiwiYm9keSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\logicleap2\logicleapweb\app\providers.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./app/test-ip/page.tsx":
/*!******************************!*\
  !*** ./app/test-ip/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\logicleap2\logicleapweb\app\test-ip\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"64x64\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL2FwcC9mYXZpY29uLmljbz9jMjYxIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjY0eDY0XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/antd","vendor-chunks/@reduxjs","vendor-chunks/react-redux","vendor-chunks/immer","vendor-chunks/reselect","vendor-chunks/redux","vendor-chunks/use-sync-external-store","vendor-chunks/redux-thunk","vendor-chunks/@ant-design","vendor-chunks/mime-db","vendor-chunks/@rc-component","vendor-chunks/axios","vendor-chunks/rc-field-form","vendor-chunks/rc-menu","vendor-chunks/rc-util","vendor-chunks/rc-tabs","vendor-chunks/@ctrl","vendor-chunks/resize-observer-polyfill","vendor-chunks/rc-motion","vendor-chunks/rc-pagination","vendor-chunks/@babel","vendor-chunks/rc-textarea","vendor-chunks/rc-input","vendor-chunks/follow-redirects","vendor-chunks/rc-dialog","vendor-chunks/rc-overflow","vendor-chunks/debug","vendor-chunks/stylis","vendor-chunks/rc-collapse","vendor-chunks/form-data","vendor-chunks/rc-resize-observer","vendor-chunks/rc-dropdown","vendor-chunks/asynckit","vendor-chunks/rc-tooltip","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/copy-to-clipboard","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/@emotion","vendor-chunks/delayed-stream","vendor-chunks/classnames","vendor-chunks/rc-picker","vendor-chunks/toggle-selection","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftest-ip%2Fpage&page=%2Ftest-ip%2Fpage&appPaths=%2Ftest-ip%2Fpage&pagePath=private-next-app-dir%2Ftest-ip%2Fpage.tsx&appDir=F%3A%5Clogicleap2%5Clogicleapweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Clogicleap2%5Clogicleapweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();