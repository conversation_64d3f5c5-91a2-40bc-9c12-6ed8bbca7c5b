/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/test-ip/page";
exports.ids = ["app/test-ip/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftest-ip%2Fpage&page=%2Ftest-ip%2Fpage&appPaths=%2Ftest-ip%2Fpage&pagePath=private-next-app-dir%2Ftest-ip%2Fpage.tsx&appDir=F%3A%5Clogicleap2%5Clogicleapweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Clogicleap2%5Clogicleapweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftest-ip%2Fpage&page=%2Ftest-ip%2Fpage&appPaths=%2Ftest-ip%2Fpage&pagePath=private-next-app-dir%2Ftest-ip%2Fpage.tsx&appDir=F%3A%5Clogicleap2%5Clogicleapweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Clogicleap2%5Clogicleapweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9100\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'test-ip',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/test-ip/page.tsx */ \"(rsc)/./app/test-ip/page.tsx\")), \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: \"/manifest.json\"\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: \"/manifest.json\"\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/test-ip/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/test-ip/page\",\n        pathname: \"/test-ip\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftest-ip%2Fpage&page=%2Ftest-ip%2Fpage&appPaths=%2Ftest-ip%2Fpage&pagePath=private-next-app-dir%2Ftest-ip%2Fpage.tsx&appDir=F%3A%5Clogicleap2%5Clogicleapweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Clogicleap2%5Clogicleapweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cantd%5C%5Cdist%5C%5Creset.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cantd%5C%5Cdist%5C%5Creset.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(ssr)/./app/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNsb2dpY2xlYXAyJTVDJTVDbG9naWNsZWFwd2ViJTVDJTVDYXBwJTVDJTVDcHJvdmlkZXJzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlByb3ZpZGVycyUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJGJTNBJTVDJTVDbG9naWNsZWFwMiU1QyU1Q2xvZ2ljbGVhcHdlYiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJGJTNBJTVDJTVDbG9naWNsZWFwMiU1QyU1Q2xvZ2ljbGVhcHdlYiU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNsb2dpY2xlYXAyJTVDJTVDbG9naWNsZWFwd2ViJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDYW50ZCU1QyU1Q2Rpc3QlNUMlNUNyZXNldC5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtKQUFvSCIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8/YWYyNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlByb3ZpZGVyc1wiXSAqLyBcIkY6XFxcXGxvZ2ljbGVhcDJcXFxcbG9naWNsZWFwd2ViXFxcXGFwcFxcXFxwcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cantd%5C%5Cdist%5C%5Creset.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Capp%5C%5Ctest-ip%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Capp%5C%5Ctest-ip%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/test-ip/page.tsx */ \"(ssr)/./app/test-ip/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNsb2dpY2xlYXAyJTVDJTVDbG9naWNsZWFwd2ViJTVDJTVDYXBwJTVDJTVDdGVzdC1pcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SkFBeUYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvP2VhM2QiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJGOlxcXFxsb2dpY2xlYXAyXFxcXGxvZ2ljbGVhcHdlYlxcXFxhcHBcXFxcdGVzdC1pcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Capp%5C%5Ctest-ip%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-redux */ \"(ssr)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./lib/store.ts\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_2__.Provider, {\n        store: _lib_store__WEBPACK_IMPORTED_MODULE_1__.store,\n        children: children\n    }, void 0, false, {\n        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\providers.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcHJvdmlkZXJzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFc0M7QUFDSDtBQUU1QixTQUFTRSxVQUFVLEVBQUVDLFFBQVEsRUFBaUM7SUFDbkUscUJBQ0UsOERBQUNILGlEQUFRQTtRQUFDQyxPQUFPQSw2Q0FBS0E7a0JBQ25CRTs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9hcHAvcHJvdmlkZXJzLnRzeD9jZTQ2Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xyXG5cclxuaW1wb3J0IHsgUHJvdmlkZXIgfSBmcm9tICdyZWFjdC1yZWR1eCdcclxuaW1wb3J0IHsgc3RvcmUgfSBmcm9tICdAL2xpYi9zdG9yZSdcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBQcm92aWRlcnMoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8UHJvdmlkZXIgc3RvcmU9e3N0b3JlfT5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9Qcm92aWRlcj5cclxuICApXHJcbn0gIl0sIm5hbWVzIjpbIlByb3ZpZGVyIiwic3RvcmUiLCJQcm92aWRlcnMiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./app/test-ip/page.tsx":
/*!******************************!*\
  !*** ./app/test-ip/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestIPPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _lib_request__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/request */ \"(ssr)/./lib/request.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst { Title, Text, Paragraph } = _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\nfunction TestIPPage() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [realPublicIP, setRealPublicIP] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 获取真实的公网IP地址\n    const getRealPublicIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83C\\uDF0D [Frontend] 开始获取真实公网IP...\");\n            // 使用多个IP查询服务，提高成功率\n            const ipServices = [\n                \"https://api.ipify.org?format=json\",\n                \"https://ipapi.co/json/\",\n                \"https://httpbin.org/ip\",\n                \"https://api.ip.sb/ip\",\n                \"https://ifconfig.me/ip\",\n                \"https://icanhazip.com\"\n            ];\n            for (const service of ipServices){\n                try {\n                    console.log(`🔍 尝试获取公网IP: ${service}`);\n                    const controller = new AbortController();\n                    const timeoutId = setTimeout(()=>controller.abort(), 5000);\n                    const response = await fetch(service, {\n                        method: \"GET\",\n                        signal: controller.signal\n                    });\n                    clearTimeout(timeoutId);\n                    if (!response.ok) continue;\n                    let data;\n                    const contentType = response.headers.get(\"content-type\");\n                    if (contentType && contentType.includes(\"application/json\")) {\n                        data = await response.json();\n                        const ip = data.ip || data.origin || data.query;\n                        if (ip && isValidIPAddress(ip)) {\n                            console.log(`✅ 成功获取公网IP: ${ip} (来源: ${service})`);\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    } else {\n                        const text = await response.text();\n                        const ip = text.trim();\n                        if (isValidIPAddress(ip)) {\n                            console.log(`✅ 成功获取公网IP: ${ip} (来源: ${service})`);\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    }\n                } catch (error) {\n                    console.log(`❌ 获取公网IP失败: ${service} - ${error}`);\n                    continue;\n                }\n            }\n            throw new Error(\"所有公网IP服务都无法访问\");\n        } catch (error) {\n            console.error(\"❌ [Frontend] 获取真实公网IP失败:\", error);\n            setResult({\n                type: \"error\",\n                error: error.message,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 验证IP地址格式\n    const isValidIPAddress = (ip)=>{\n        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n        return ipv4Regex.test(ip);\n    };\n    // 测试获取当前IP位置\n    const testCurrentIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend] 开始测试当前IP获取...\");\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"/api/v1/ip-location/current\");\n            console.log(\"\\uD83D\\uDCE5 [Frontend] 收到响应:\", response);\n            setResult({\n                type: \"current-ip\",\n                data: response.data,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            console.error(\"❌ [Frontend] 请求失败:\", error);\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: error.response?.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试查询指定IP\n    const testQueryIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend] 开始测试IP查询...\");\n            const testIP = \"*******\"; // Google DNS\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(`/api/v1/ip-location/query?ip=${testIP}&includeRisk=false`);\n            console.log(\"\\uD83D\\uDCE5 [Frontend] 收到响应:\", response);\n            setResult({\n                type: \"query-ip\",\n                testIP,\n                data: response.data,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            console.error(\"❌ [Frontend] 请求失败:\", error);\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: error.response?.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试登录接口（观察IP日志）\n    const testLoginIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend] 开始测试登录IP获取...\");\n            // 这里故意使用错误的登录信息，只是为了触发IP获取逻辑\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"/api/user-auth/password\", {\n                phone: \"12345678910\",\n                password: \"123456\"\n            });\n            console.log(\"\\uD83D\\uDCE5 [Frontend] 登录响应:\", response);\n            setResult({\n                type: \"login-test\",\n                data: response.data,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCDD [Frontend] 登录测试完成 (预期失败):\", error);\n            setResult({\n                type: \"login-test\",\n                error: error.message,\n                response: error.response?.data,\n                note: \"这是预期的失败，主要用于观察IP获取日志\",\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"24px\",\n            maxWidth: \"1200px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                level: 2,\n                children: \"\\uD83E\\uDDEA IP地址获取测试页面\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                children: \"这个页面用于测试前端到后端的IP地址传递和获取功能。 请打开浏览器开发者工具的控制台，以及后端服务器的日志，观察IP获取过程。\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                direction: \"vertical\",\n                size: \"large\",\n                style: {\n                    width: \"100%\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDFAF 测试功能\",\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                wrap: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        type: \"primary\",\n                                        loading: loading,\n                                        onClick: getRealPublicIP,\n                                        style: {\n                                            background: \"#52c41a\",\n                                            borderColor: \"#52c41a\"\n                                        },\n                                        children: \"\\uD83C\\uDF0D 获取真实公网IP\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testCurrentIP,\n                                        children: \"测试获取当前IP位置\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testQueryIP,\n                                        children: \"测试查询指定IP (*******)\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testLoginIP,\n                                        danger: true,\n                                        children: \"测试登录IP获取 (会失败)\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            realPublicIP && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: \"12px\",\n                                    padding: \"8px\",\n                                    background: \"#f6ffed\",\n                                    border: \"1px solid #b7eb8f\",\n                                    borderRadius: \"6px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    style: {\n                                        color: \"#52c41a\"\n                                    },\n                                    children: [\n                                        \"\\uD83C\\uDF0D 你的真实公网IP: \",\n                                        realPublicIP\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCA 测试结果\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: \"#f5f5f5\",\n                                padding: \"16px\",\n                                borderRadius: \"6px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                style: {\n                                    margin: 0,\n                                    fontSize: \"12px\",\n                                    lineHeight: \"1.4\"\n                                },\n                                children: JSON.stringify(result, null, 2)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCB 观察要点\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83C\\uDF10 前端中间件日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看浏览器控制台，观察 [Middleware] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDDA5️ 后端IP提取日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [Backend] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD10 登录日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [LoginLog] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD0D 重点观察：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"IP地址是否从前端正确传递到后端，以及各个环节的IP获取情况\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDF0D 当前环境信息\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"浏览器 User-Agent: \",\n                                         false ? 0 : \"服务端渲染\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"当前时间: \",\n                                        new Date().toISOString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"页面URL: \",\n                                         false ? 0 : \"服务端渲染\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/test-ip/page.tsx\n");

/***/ }),

/***/ "(ssr)/./config/config.ts":
/*!**************************!*\
  !*** ./config/config.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_HOST: () => (/* binding */ API_HOST),\n/* harmony export */   API_PORT: () => (/* binding */ API_PORT),\n/* harmony export */   API_URL: () => (/* binding */ API_URL),\n/* harmony export */   BASE_URL: () => (/* binding */ BASE_URL),\n/* harmony export */   CONFIG: () => (/* binding */ CONFIG),\n/* harmony export */   DEV_CONFIG: () => (/* binding */ DEV_CONFIG),\n/* harmony export */   PROD_CONFIG: () => (/* binding */ PROD_CONFIG),\n/* harmony export */   SERVER_URL: () => (/* binding */ SERVER_URL),\n/* harmony export */   WS_URL: () => (/* binding */ WS_URL)\n/* harmony export */ });\n/*\r\n * @Author: Zwww <EMAIL>\r\n * @Date: 2025-04-27 23:58:17\r\n * @LastEditors: Zwww <EMAIL>\r\n * @LastEditTime: 2025-04-28 09:36:46\r\n * @FilePath: \\logicleapweb\\config\\config.ts\r\n * @Description: \r\n * \r\n * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. \r\n */ // 开发环境配置\nconst DEV_CONFIG = {\n    API_HOST: \"http://127.0.0.1\",\n    API_PORT: \"8601\",\n    BASE_URL: \"${API_HOST}:${API_PORT}\",\n    SERVER_URL: \"http://127.0.0.1:8602\",\n    API_URL: \"http://127.0.0.1:8003\",\n    WS_URL: \"http://localhost:8003\"\n};\n// 生产环境配置\nconst PROD_CONFIG = {\n    API_HOST: \"https://www.logicleapai.cn\",\n    API_PORT: \"8601\",\n    BASE_URL: \"https://www.logicleapai.cn\",\n    SERVER_URL: \"https://www.logicleapai.cn/server\",\n    API_URL: \"https://www.logicleapai.cn/api\",\n    WS_URL: \"wss://www.logicleapai.cn/api\",\n    WS_SERVER_URL: \"wss://logicleapai.cn/server/ws\"\n};\n// 根据环境导出相应配置\nconst CONFIG =  false ? 0 : DEV_CONFIG;\n// 导出单独的配置项,方便直接使用\nconst { API_HOST, API_PORT, BASE_URL, SERVER_URL, API_URL, WS_URL } = CONFIG;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./config/config.ts\n");

/***/ }),

/***/ "(ssr)/./lib/request.ts":
/*!************************!*\
  !*** ./lib/request.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   hasShownModal: () => (/* binding */ hasShownModal),\n/* harmony export */   resetModalFlag: () => (/* binding */ resetModalFlag)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _config_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../config/config */ \"(ssr)/./config/config.ts\");\n/* harmony import */ var _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Modal!=!antd */ \"(ssr)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/store */ \"(ssr)/./lib/store.ts\");\n\n\n\n\n\nconst request = axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n    baseURL: _config_config__WEBPACK_IMPORTED_MODULE_0__.API_URL,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\nlet isTokenExpiredGlobal = false;\n// 添加全局标志，用于控制弹框显示\nlet hasShownModal = false;\n// 添加重置方法\nconst resetModalFlag = ()=>{\n    hasShownModal = false;\n};\n// 刷新token相关变量\nlet isRefreshing = false;\nlet failedQueue = [];\n// 处理队列中的请求\nconst processQueue = (error, token = null)=>{\n    failedQueue.forEach(({ resolve, reject, config })=>{\n        if (error) {\n            reject(error);\n        } else {\n            if (token) {\n                config.headers.Authorization = token;\n            }\n            resolve(request(config));\n        }\n    });\n    failedQueue = [];\n};\n// 刷新token的函数\nconst refreshToken = async ()=>{\n    const refreshToken = localStorage.getItem(\"refreshToken\");\n    console.log(\"\\uD83D\\uDD04 开始刷新token，refreshToken:\", refreshToken ? `${refreshToken.substring(0, 20)}...` : \"无\");\n    if (!refreshToken) {\n        console.error(\"❌ 没有refreshToken，无法刷新\");\n        throw new Error(\"No refresh token available\");\n    }\n    try {\n        console.log(\"\\uD83D\\uDCE4 发送刷新token请求到:\", \"/api/router-guard/refresh-token\");\n        const response = await axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"/api/router-guard/refresh-token\", {\n            refreshToken: refreshToken\n        }, {\n            baseURL: _config_config__WEBPACK_IMPORTED_MODULE_0__.API_URL\n        });\n        console.log(\"\\uD83D\\uDCE5 刷新token响应:\", response.data);\n        if (response.data.code === 200) {\n            const { token, refreshToken: newRefreshToken } = response.data.data;\n            console.log(\"✅ 刷新token成功，新token:\", token ? `${token.substring(0, 20)}...` : \"无\");\n            localStorage.setItem(\"token\", token);\n            localStorage.setItem(\"refreshToken\", newRefreshToken);\n            return token;\n        } else {\n            console.error(\"❌ 刷新token失败，响应码:\", response.data.code, \"消息:\", response.data.message || response.data.msg);\n            throw new Error(`Token refresh failed: ${response.data.message || response.data.msg || \"Unknown error\"}`);\n        }\n    } catch (error) {\n        console.error(\"❌ 刷新token异常:\", error);\n        console.error(\"错误详情:\", {\n            message: error.message,\n            response: error.response?.data,\n            status: error.response?.status\n        });\n        // 刷新失败，清除所有token\n        localStorage.removeItem(\"token\");\n        localStorage.removeItem(\"refreshToken\");\n        localStorage.removeItem(\"user\");\n        (0,_lib_store__WEBPACK_IMPORTED_MODULE_1__.clearUser)();\n        throw error;\n    }\n};\n// 请求拦截器\nrequest.interceptors.request.use(async (config)=>{\n    const token = localStorage.getItem(\"token\");\n    const refreshTokenValue = localStorage.getItem(\"refreshToken\");\n    // 检查请求的URL是否为登录接口或刷新token接口\n    if (config.url && (config.url.includes(\"/api/user-auth/password\") || config.url.includes(\"/api/router-guard/refresh-token\"))) {\n        return config; // 不拦截登录和刷新token请求\n    }\n    if (token) {\n        config.headers.Authorization = token;\n    } else if (refreshTokenValue && !isRefreshing) {\n        // 没有token但有refreshToken，尝试主动刷新\n        console.log(\"\\uD83D\\uDD04 请求拦截器检测到缺少token但有refreshToken，主动尝试刷新\");\n        try {\n            // 标记正在刷新，避免重复刷新\n            isRefreshing = true;\n            const newToken = await refreshToken();\n            console.log(\"✅ 请求拦截器中刷新token成功\");\n            // 设置新token到当前请求\n            config.headers.Authorization = newToken;\n            // 处理队列中的其他请求\n            processQueue(null, newToken);\n        } catch (refreshError) {\n            console.error(\"❌ 请求拦截器中刷新token失败:\", refreshError);\n            // 处理队列中的其他请求\n            processQueue(refreshError, null);\n            // 刷新失败，清除refreshToken并拒绝请求\n            handleLogout(\"请求拦截器中refreshToken刷新失败\");\n            return Promise.reject(new Error(\"Token刷新失败，请重新登录\"));\n        } finally{\n            isRefreshing = false;\n        }\n    } else if (!refreshTokenValue) {\n        console.warn(\"请求拦截器 - 未找到token和refreshToken\");\n    } else {\n        console.warn(\"请求拦截器 - 未找到token，但正在刷新中\");\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\nrequest.interceptors.response.use((response)=>{\n    if (response.config.url?.includes(\"/login/password\") && response.data?.code === 200) {\n        resetModalFlag();\n        isTokenExpiredGlobal = false; // 重置token过期标志\n    }\n    return response;\n}, async (error)=>{\n    const originalRequest = error.config;\n    const errorData = error.response?.data;\n    // 处理401状态码的错误\n    if (error.response?.status === 401) {\n        console.log(\"\\uD83D\\uDEA8 收到401错误:\", {\n            url: originalRequest?.url,\n            status: error.response?.status,\n            errorData: errorData,\n            hasType: !!errorData?.type,\n            hasMsg: !!errorData?.msg\n        });\n        // 处理token过期和其他设备登录的情况\n        // 检查多种可能的错误格式和消息\n        // 支持嵌套的错误结构（如 details 字段）\n        const detailsData = errorData?.details || errorData;\n        // 检查是否是其他设备登录的错误\n        const isOtherDeviceLogin = errorData?.type === \"OTHER_DEVICE_LOGIN\" || detailsData?.type === \"OTHER_DEVICE_LOGIN\" || errorData?.msg && errorData.msg.includes(\"账号已在其他设备登录\") || detailsData?.msg && detailsData.msg.includes(\"账号已在其他设备登录\") || errorData?.message && errorData.message.includes(\"账号已在其他设备登录\") || detailsData?.message && detailsData.message.includes(\"账号已在其他设备登录\");\n        console.log(\"\\uD83D\\uDD0D 检查其他设备登录状态:\", {\n            isOtherDeviceLogin,\n            errorData: errorData,\n            detailsData: detailsData,\n            hasShownModal\n        });\n        // 如果是其他设备登录，直接显示提示\n        if (isOtherDeviceLogin && !hasShownModal) {\n            hasShownModal = true;\n            _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].confirm({\n                title: \"账号异常\",\n                content: \"您的账号已在其他设备登录，当前登录已失效，请重新登录\",\n                okText: \"重新登录\",\n                maskClosable: false,\n                keyboard: true,\n                centered: true,\n                className: \"other-device-login-modal\",\n                closable: false,\n                cancelButtonProps: {\n                    style: {\n                        display: \"none\"\n                    }\n                },\n                onOk: ()=>{\n                    handleLogout(\"其他设备登录，当前会话失效\");\n                }\n            });\n            return Promise.reject(error);\n        }\n        // 处理token过期的情况，尝试无感刷新\n        // 使用上面已定义的 detailsData 变量\n        const isTokenExpired = [\n            \"TOKEN_EXPIRED\",\n            \"INVALID_TOKEN\"\n        ].includes(errorData?.type || detailsData?.type) || errorData?.msg && (errorData.msg.includes(\"登录已过期\") || errorData.msg.includes(\"token无效\") || errorData.msg.includes(\"token已过期\") || errorData.msg.includes(\"请先登录\")) || detailsData?.msg && (detailsData.msg.includes(\"登录已过期\") || detailsData.msg.includes(\"token无效\") || detailsData.msg.includes(\"token已过期\") || detailsData.msg.includes(\"请先登录\")) || errorData?.message && (errorData.message.includes(\"登录已过期\") || errorData.message.includes(\"token无效\") || errorData.message.includes(\"token已过期\") || errorData.message.includes(\"请先登录\")) || detailsData?.message && (detailsData.message.includes(\"登录已过期\") || detailsData.message.includes(\"token无效\") || detailsData.message.includes(\"token已过期\") || detailsData.message.includes(\"请先登录\")) || error.response?.status === 401 && (errorData?.code === 401 || detailsData?.code === 401);\n        console.log(\"\\uD83D\\uDD0D 检查token过期状态:\", {\n            isTokenExpired,\n            errorData: errorData,\n            detailsData: detailsData,\n            errorType: errorData?.type,\n            errorMsg: errorData?.msg,\n            errorMessage: errorData?.message,\n            errorCode: errorData?.code,\n            detailsType: detailsData?.type,\n            detailsMsg: detailsData?.msg,\n            detailsMessage: detailsData?.message,\n            detailsCode: detailsData?.code,\n            status: error.response?.status,\n            url: originalRequest?.url\n        });\n        if (isTokenExpired) {\n            console.log(\"\\uD83D\\uDD0D 检测到token过期，准备无感刷新\");\n            // 如果已经在刷新中，将请求加入队列\n            if (isRefreshing) {\n                console.log(\"⏳ 已在刷新中，将请求加入队列\");\n                return new Promise((resolve, reject)=>{\n                    failedQueue.push({\n                        resolve,\n                        reject,\n                        config: originalRequest\n                    });\n                });\n            }\n            // 如果没有refreshToken，直接登出\n            const refreshTokenValue = localStorage.getItem(\"refreshToken\");\n            if (!refreshTokenValue) {\n                console.log(\"❌ 没有refreshToken，直接登出\");\n                handleLogout(\"缺少refreshToken\");\n                return Promise.reject(error);\n            }\n            // 开始刷新token\n            console.log(\"\\uD83D\\uDD04 开始刷新token流程\");\n            isRefreshing = true;\n            try {\n                const newToken = await refreshToken();\n                console.log(\"✅ 刷新token成功，处理队列中的请求\");\n                processQueue(null, newToken);\n                // 重新发起原始请求\n                originalRequest.headers.Authorization = newToken;\n                console.log(\"\\uD83D\\uDD01 重新发起原始请求:\", originalRequest.url);\n                return request(originalRequest);\n            } catch (refreshError) {\n                console.error(\"❌ 刷新token失败:\", refreshError);\n                processQueue(refreshError, null);\n                handleLogout(\"refreshToken刷新失败\");\n                return Promise.reject(refreshError);\n            } finally{\n                isRefreshing = false;\n            }\n        }\n        // 其他401错误，直接登出\n        if (!isTokenExpiredGlobal) {\n            isTokenExpiredGlobal = true;\n            handleLogout(\"非token过期的401错误\");\n        }\n    }\n    // 错误仍然需要传递给调用者处理\n    return Promise.reject(error);\n});\n// 统一的登出处理函数\nconst handleLogout = (reason = \"未知原因\")=>{\n    console.log(\"\\uD83D\\uDEAA 执行登出操作，原因:\", reason);\n    console.log(\"\\uD83D\\uDDD1️ 清除localStorage中的认证信息\");\n    // 记录清除前的状态\n    const beforeClear = {\n        token: localStorage.getItem(\"token\") ? \"存在\" : \"不存在\",\n        refreshToken: localStorage.getItem(\"refreshToken\") ? \"存在\" : \"不存在\",\n        user: localStorage.getItem(\"user\") ? \"存在\" : \"不存在\"\n    };\n    console.log(\"清除前状态:\", beforeClear);\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    localStorage.removeItem(\"refreshToken\");\n    _lib_store__WEBPACK_IMPORTED_MODULE_1__.store.dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_1__.clearUser)());\n    console.log(\"✅ 登出处理完成\");\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (request);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/request.ts\n");

/***/ }),

/***/ "(ssr)/./lib/store.ts":
/*!**********************!*\
  !*** ./lib/store.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearUser: () => (/* binding */ clearUser),\n/* harmony export */   selectUserState: () => (/* binding */ selectUserState),\n/* harmony export */   setUser: () => (/* binding */ setUser),\n/* harmony export */   store: () => (/* binding */ store),\n/* harmony export */   userSlice: () => (/* binding */ userSlice)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/*\r\n * @Author: Zwww <EMAIL>\r\n * @Date: 2025-04-25 12:31:25\r\n * @LastEditors: Zwww <EMAIL>\r\n * @LastEditTime: 2025-05-01 13:19:36\r\n * @FilePath: \\logicleapweb\\lib\\store.ts\r\n * @Description: \r\n * \r\n * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. \r\n */ \nconst defaultUserState = {\n    userId: null,\n    nickName: \"\",\n    avatarUrl: \"\",\n    introduction: \"\",\n    gender: 0,\n    phone: \"\",\n    isLoggedIn: false,\n    roleId: null,\n    registerType: \"\"\n};\n// 从 localStorage 获取初始状态\nconst loadInitialState = ()=>{\n    if (false) {}\n    return {\n        userState: {\n            ...defaultUserState\n        }\n    };\n};\n// 创建 userSlice\nconst userSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: \"user\",\n    initialState: loadInitialState(),\n    reducers: {\n        setUser: (state, action)=>{\n            state.userState = {\n                ...state.userState,\n                ...action.payload,\n                isLoggedIn: true,\n                lastUpdateTime: Date.now()\n            };\n            if (false) {}\n        },\n        clearUser: (state)=>{\n            state.userState = {\n                userId: null,\n                nickName: \"\",\n                avatarUrl: \"\",\n                introduction: \"\",\n                gender: 0,\n                phone: \"\",\n                isLoggedIn: false,\n                roleId: null,\n                registerType: \"\",\n                lastUpdateTime: null\n            };\n            // 如果是在浏览器环境中，则清除localStorage中的用户信息\n            if (false) {}\n        }\n    }\n});\nconst { setUser, clearUser } = userSlice.actions;\n// 创建 store\nconst store = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.configureStore)({\n    reducer: {\n        [userSlice.name]: userSlice.reducer\n    },\n    middleware: (getDefaultMiddleware)=>getDefaultMiddleware({\n            serializableCheck: false\n        })\n});\nconst selectUserState = (state)=>state.user.userState;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/store.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"928949eb63fa\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9hcHAvZ2xvYmFscy5jc3M/MTgwMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjkyODk0OWViNjNmYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var antd_dist_reset_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! antd/dist/reset.css */ \"(rsc)/./node_modules/antd/dist/reset.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./providers */ \"(rsc)/./app/providers.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Logic Leap\",\n    description: \"为青少年打造的AI学习平台\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {}, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_3__.Providers, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQU1NQTtBQUpnQjtBQUNNO0FBQ1c7QUFJaEMsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFDYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSzs7MEJBQ1QsOERBQUNDOzs7OzswQkFFRCw4REFBQ0M7MEJBQ0MsNEVBQUNULGlEQUFTQTs4QkFDUEs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xyXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXHJcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcclxuaW1wb3J0ICdhbnRkL2Rpc3QvcmVzZXQuY3NzJ1xyXG5pbXBvcnQgeyBQcm92aWRlcnMgfSBmcm9tICcuL3Byb3ZpZGVycydcclxuXHJcbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcclxuXHJcbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XHJcbiAgdGl0bGU6ICdMb2dpYyBMZWFwJyxcclxuICBkZXNjcmlwdGlvbjogJ+S4uumdkuWwkeW5tOaJk+mAoOeahEFJ5a2m5Lmg5bmz5Y+wJyxcclxufVxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcclxuICBjaGlsZHJlbixcclxufToge1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcclxufSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cclxuICAgICAgPGhlYWQ+XHJcbiAgICAgIDwvaGVhZD5cclxuICAgICAgPGJvZHk+XHJcbiAgICAgICAgPFByb3ZpZGVycz5cclxuICAgICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICA8L1Byb3ZpZGVycz5cclxuICAgICAgPC9ib2R5PlxyXG4gICAgPC9odG1sPlxyXG4gIClcclxufVxyXG5cclxuIl0sIm5hbWVzIjpbImludGVyIiwiUHJvdmlkZXJzIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJoZWFkIiwiYm9keSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\logicleap2\logicleapweb\app\providers.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./app/test-ip/page.tsx":
/*!******************************!*\
  !*** ./app/test-ip/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\logicleap2\logicleapweb\app\test-ip\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"64x64\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL2FwcC9mYXZpY29uLmljbz9jMjYxIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjY0eDY0XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/antd","vendor-chunks/@reduxjs","vendor-chunks/react-redux","vendor-chunks/immer","vendor-chunks/reselect","vendor-chunks/redux","vendor-chunks/use-sync-external-store","vendor-chunks/redux-thunk","vendor-chunks/@ant-design","vendor-chunks/mime-db","vendor-chunks/@rc-component","vendor-chunks/axios","vendor-chunks/rc-field-form","vendor-chunks/rc-menu","vendor-chunks/rc-util","vendor-chunks/rc-tabs","vendor-chunks/@ctrl","vendor-chunks/resize-observer-polyfill","vendor-chunks/rc-motion","vendor-chunks/rc-pagination","vendor-chunks/@babel","vendor-chunks/rc-textarea","vendor-chunks/rc-input","vendor-chunks/follow-redirects","vendor-chunks/rc-dialog","vendor-chunks/rc-overflow","vendor-chunks/debug","vendor-chunks/stylis","vendor-chunks/rc-collapse","vendor-chunks/form-data","vendor-chunks/rc-resize-observer","vendor-chunks/rc-dropdown","vendor-chunks/asynckit","vendor-chunks/rc-tooltip","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/copy-to-clipboard","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/@emotion","vendor-chunks/delayed-stream","vendor-chunks/classnames","vendor-chunks/rc-picker","vendor-chunks/toggle-selection","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftest-ip%2Fpage&page=%2Ftest-ip%2Fpage&appPaths=%2Ftest-ip%2Fpage&pagePath=private-next-app-dir%2Ftest-ip%2Fpage.tsx&appDir=F%3A%5Clogicleap2%5Clogicleapweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Clogicleap2%5Clogicleapweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();